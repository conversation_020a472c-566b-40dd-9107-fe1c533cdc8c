#include <iostream>
#include <vector>
#include <algorithm>

using namespace std;

struct Item {
    string title;
    double volume_per_kg;
    double calories_per_kg;
};

struct Activity {
    int index;
    int begin;
    int end;
};

struct CoinResult {
    int total_count;
    vector<pair<int, int>> used_coins; // пара (номінал, кількість)
};

void backpackWithRectrictions(double max_volume) {
    vector<Item> products = {
        {"М'ясо", 1.0, 1500},
        {"Борошно", 1.5, 5000},
        {"Молоко", 2.0, 5000},
        {"Цукор", 1.0, 4000},
    };

    double max_calories = 0;
    vector<double> best_weights(4, 0);

    for (double sugar = 0; sugar <= max_volume / 1.0; sugar += 0.1) {
        double min_milk = sugar * 8;
        double min_flour = min_milk;
        double min_meat = 2 * min_flour;

        // Обчислюємо загальний об'єм
        for (int i = 0; i < 4; ++i) {
            int value2 = products[i].volume_per_kg;
            double total_volume = min_meat * value2 +
                             min_flour * value2 +
                             min_milk * products[2].volume_per_kg +
                             sugar * products[3].volume_per_kg;
        }
        double total_volume = min_meat * products[0].volume_per_kg +
                             min_flour * products[1].volume_per_kg +
                             min_milk * products[2].volume_per_kg +
                             sugar * products[3].volume_per_kg;

        if (total_volume <= max_volume) {
            double calories = min_meat * products[0].calories_per_kg +
                             min_flour * products[1].calories_per_kg +
                             min_milk * products[2].calories_per_kg +
                             sugar * products[3].calories_per_kg;

            if (calories > max_calories) {
                max_calories = calories;
                best_weights[0] = min_meat;
                best_weights[1] = min_flour;
                best_weights[2] = min_milk;
                best_weights[3] = sugar;
            }
        }
    }

    cout << "Максимальна калорійність: " << max_calories << " ккал\n";
    cout << "Продукти для пакування:\n";
    cout << "М'ясо: " << best_weights[0] << " кг\n";
    cout << "Борошно: " << best_weights[1] << " кг\n";
    cout << "Молоко: " << best_weights[2] << " кг\n";
    cout << "Цукор: " << best_weights[3] << " кг\n";
}

bool compare(const Activity &a, const Activity &b) {
    return a.end < b.end;
}

void activitySelection(vector<Activity> &activities) {
    sort(activities.begin(), activities.end(), compare);

    vector<Activity> selected;
    int last_end_time = -1;

    for (const auto &activity : activities) {
        if (activity.begin >= last_end_time) {
            selected.push_back(activity);
            last_end_time = activity.end;
        }
    }

    cout << "Максимальна кількість занять: " << selected.size() << "\n";
    cout << "Вибрані заняття (індекс, початок, кінець):\n";
    for (const auto &activity : selected) {
        cout << activity.index << ": (" << activity.begin << ", " << activity.end << ")\n";
    }
}



CoinResult task(vector<int> numbers, int sum) {
    sort(numbers.begin(), numbers.end(), greater<int>());

    CoinResult result;
    result.total_count = 0;
    int remaining_sum = sum;

    for (int coin : numbers) {
        int coin_count = 0;
        while (remaining_sum >= coin) {
            remaining_sum -= coin;
            coin_count++;
            result.total_count++;
        }
        if (coin_count > 0) {
            result.used_coins.push_back({coin, coin_count});
        }
    }

    if (remaining_sum != 0) {
        result.total_count = -1;
        result.used_coins.clear();
    }

    return result;
}

int main() {
    // int choice;
    // do {
    //     cout << "\nМеню:\n";
    //     cout << "1. Розв’язати задачу про рюкзак\n";
    //     cout << "2. Розв’язати задачу про вибір занять\n";
    //     cout << "0. Вихід\n";
    //     cout << "Виберіть опцію: ";
    //     cin >> choice;
    //
    //     switch (choice) {
    //         case 1: {
    //             double max_volume;
    //             cout << "Введіть об'єм рюкзака (дм³): ";
    //             cin >> max_volume;
    //             backpackWithRectrictions(max_volume);
    //             break;
    //         }
    //         case 2: {
    //             int n;
    //             cout << "Введіть кількість занять: ";
    //             cin >> n;
    //             vector<Activity> activities(n);
    //             cout << "Введіть час початку та закінчення для кожного заняття:\n";
    //             for (int i = 0; i < n; ++i) {
    //                 activities[i].index = i + 1;
    //                 cout << "Заняття " << i + 1 << " (початок кінець): ";
    //                 cin >> activities[i].begin >> activities[i].end;
    //             }
    //             activitySelection(activities);
    //             break;
    //         }
    //         case 0:
    //             cout << "Програма завершена.\n";
    //             break;
    //         default:
    //             cout << "Невірний вибір. Спробуйте ще раз.\n";
    //     }
    // } while (choice != 0);

    vector<int> numbers = {1, 5, 10, 20}; // Номінали купюр
    int target_sum;

    cout << "Введіть цільову суму (грн): ";
    cin >> target_sum;

    CoinResult result = task(numbers, target_sum);

    if (result.total_count == -1) {
        cout << "Неможливо зібрати суму " << target_sum << " грн цими номіналами.\n";
    }
    else {
        cout << "Мінімальна кількість купюр: " << result.total_count << "\n";
        cout << "Використані номінали:\n";
        for (const auto& coin_pair : result.used_coins) {
            cout << "Номінал " << coin_pair.first << " грн: " << coin_pair.second << " шт.\n";
        }
    }
}